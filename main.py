#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام إدارة الأعمال المتكامل
==========================

تطبيق شامل لإدارة الحسابات والمشاريع والعملاء باستخدام PyQt5 و SQLAlchemy

الميزات الرئيسية:
- إدارة العملاء والموردين والموظفين
- نظام الفواتير والمبيعات والمشتريات
- إدارة المشاريع والعقارات
- نظام التقارير المالية المتقدم
- أنظمة الأمان والنسخ الاحتياطي
- مراقبة الأداء والصلاحيات

المؤلف: نظام إدارة الأعمال
الإصدار: 2.0
التاريخ: 2025
"""

import sys
import os
import warnings
import traceback
from PyQt5.QtWidgets import QApplication, QMessageBox
from PyQt5.QtCore import Qt, qInstallMessageHandler, QtMsgType, QTimer
from PyQt5.QtGui import QFont

# استيراد الأنظمة المتقدمة الجديدة
try:
    from system_initializer import initialize_advanced_systems, shutdown_advanced_systems
    ADVANCED_SYSTEMS_AVAILABLE = True
except ImportError as e:
    print(f"⚠️ الأنظمة المتقدمة غير متاحة: {e}")
    print("💡 لتفعيل جميع الميزات، قم بتثبيت المتطلبات: pip install -r requirements.txt")
    ADVANCED_SYSTEMS_AVAILABLE = False
except Exception as e:
    print(f"❌ خطأ في تحميل الأنظمة المتقدمة: {e}")
    ADVANCED_SYSTEMS_AVAILABLE = False

# إعداد إخفاء التحذيرات الشامل
warnings.filterwarnings("ignore")
os.environ['PYTHONWARNINGS'] = 'ignore'
os.environ['QT_LOGGING_RULES'] = '*.debug=false;*.info=false;*.warning=false'

# إخفاء رسائل CSS غير المدعومة نهائياً
def qt_message_handler(mode, _, message):
    """مرشح رسائل Qt لإخفاء رسائل CSS غير المدعومة"""
    # قائمة الرسائل المراد إخفاؤها
    blocked_messages = [
        'Unknown property',
        'text-shadow',
        'box-shadow',
        'transform',
        'transition',
        'filter',
        'backdrop-filter',
        'overflow',
        'text-overflow',
        'cursor',
        'letter-spacing',
        'word-spacing',
        'text-decoration',
        'outline',
        'resize',
        'user-select',
        'pointer-events',
        'clip-path',
        'mask',
        'opacity',
        'visibility',
        'z-index',
        'position',
        'top',
        'left',
        'right',
        'bottom',
        'float',
        'clear',
        'display',
        'flex',
        'grid',
        'animation',
        'keyframes'
    ]

    # إخفاء الرسائل المحددة
    if any(blocked in message for blocked in blocked_messages):
        return

    # السماح بالرسائل الحرجة فقط
    if mode in [QtMsgType.QtCriticalMsg, QtMsgType.QtFatalMsg]:
        print(f"Qt Critical: {message}")

# تطبيق مرشح الرسائل
qInstallMessageHandler(qt_message_handler)

from ui.main_window import MainWindow
from database import init_db, get_session, User, hash_password

def setup_application():
    """
    إعداد تطبيق PyQt مع التحسينات والإعدادات المتقدمة

    يتضمن:
    - إعداد الخط الافتراضي
    - تحسين الأداء
    - إعداد معالج الرسائل
    - تحسين العرض على الشاشات عالية الدقة

    Returns:
        QApplication: مثيل التطبيق المُعد
    """
    # تعيين خيارات الأداء العالي قبل إنشاء التطبيق
    QApplication.setAttribute(Qt.AA_EnableHighDpiScaling, True)
    QApplication.setAttribute(Qt.AA_UseHighDpiPixmaps, True)
    QApplication.setAttribute(Qt.AA_DontCreateNativeWidgetSiblings, True)

    # إنشاء تطبيق PyQt
    app = QApplication(sys.argv)

    # تعيين اسم التطبيق
    app.setApplicationName("Advanced Management System")

    # تعيين نمط التطبيق
    app.setStyle("Fusion")

    # تعيين اتجاه النص من اليمين إلى اليسار للغة العربية
    app.setLayoutDirection(Qt.RightToLeft)

    # تعيين الخط الافتراضي
    try:
        font = QFont()
        font.setFamily("Arial")
        font.setPointSize(10)
        app.setFont(font)
    except:
        pass  # تجاهل أخطاء الخط

    return app

def main():
    """
    الدالة الرئيسية للتطبيق

    تتولى:
    - تهيئة التطبيق والأنظمة المتقدمة
    - إعداد قاعدة البيانات والمستخدم الإداري
    - إنشاء وعرض النافذة الرئيسية
    - معالجة الأخطاء والإغلاق الآمن

    يحل مشكلة الشاشة السوداء من خلال:
    - التهيئة المتدرجة للأنظمة
    - معالجة شاملة للأخطاء
    - إعداد صحيح لواجهة المستخدم
    """
    global ADVANCED_SYSTEMS_AVAILABLE

    try:
        print("🚀 بدء تشغيل البرنامج...")

        # تهيئة الأنظمة المتقدمة
        if ADVANCED_SYSTEMS_AVAILABLE:
            try:
                print("🔧 تهيئة الأنظمة المتقدمة...")
                advanced_success = initialize_advanced_systems()
                if advanced_success:
                    print("✅ تم تهيئة الأنظمة المتقدمة بنجاح")
                else:
                    print("⚠️ تم تهيئة الأنظمة المتقدمة مع بعض القيود")
            except Exception as e:
                print(f"❌ خطأ في تهيئة الأنظمة المتقدمة: {e}")
                ADVANCED_SYSTEMS_AVAILABLE = False

        # إعداد تطبيق PyQt
        app = setup_application()
        print("✅ تم إعداد التطبيق بنجاح")

        # إعداد قاعدة البيانات مع التحسينات المتقدمة
        print("🔧 إعداد قاعدة البيانات مع التحسينات المتقدمة...")
        init_db()
        print("✅ تم إعداد قاعدة البيانات مع التحسينات بنجاح")

        # إنشاء جلسة قاعدة البيانات
        session = get_session()
        print("✅ تم إنشاء جلسة قاعدة البيانات بنجاح")

        # الحصول على المستخدم الإداري (تم إنشاؤه في init_db)
        user = session.query(User).filter_by(role="admin").first()
        if not user:
            print("⚠️ لم يتم العثور على مستخدم إداري، سيتم إنشاؤه تلقائياً في المرة القادمة")
            # إنشاء مستخدم مؤقت للجلسة الحالية
            user = User(
                username="admin",
                password=hash_password("admin"),
                role="admin",
                full_name="المدير العام"
            )
            session.add(user)
            session.commit()
            print("✅ تم إنشاء المستخدم الإداري بنجاح")

        # إنشاء النافذة الرئيسية
        print("🚀 إنشاء النافذة الرئيسية...")
        try:
            window = MainWindow(session=session, current_user=user)
            print("✅ تم إنشاء النافذة الرئيسية بنجاح")
        except Exception as e:
            print(f"❌ خطأ في إنشاء النافذة الرئيسية: {str(e)}")
            traceback.print_exc()
            raise

        # إظهار النافذة الرئيسية
        print("📺 إظهار النافذة...")
        window.show()

        # تكبير النافذة بعد إظهارها بوقت كافي
        QTimer.singleShot(200, window.showMaximized)

        print("🎉 تم تشغيل البرنامج بنجاح!")

        # إضافة زر تطبيق الويب في شريط المهام
        try:
            def open_web_app():
                """فتح تطبيق الويب"""
                try:
                    import subprocess
                    import webbrowser
                    import time
                    import threading

                    def start_web_app():
                        try:
                            # تشغيل تطبيق الويب
                            subprocess.Popen(['python', 'final_web_app.py'],
                                           cwd=os.path.dirname(os.path.abspath(__file__)))

                            # انتظار قليل ثم فتح المتصفح
                            time.sleep(3)
                            webbrowser.open('http://localhost:5000')

                        except Exception as e:
                            print(f"خطأ في تشغيل تطبيق الويب: {e}")

                    # تشغيل في خيط منفصل
                    web_thread = threading.Thread(target=start_web_app, daemon=True)
                    web_thread.start()

                    # عرض رسالة للمستخدم
                    QMessageBox.information(
                        window,
                        "تطبيق الويب",
                        "🌐 جاري تشغيل تطبيق الويب...\n\n"
                        "سيتم فتح المتصفح تلقائياً خلال ثوان قليلة.\n\n"
                        "📱 يمكنك الوصول للتطبيق من:\n"
                        "• الكمبيوتر: http://localhost:5000\n"
                        "• الموبايل: http://[عنوان-IP]:5000\n\n"
                        "✅ التطبيق يعرض نفس البيانات من البرنامج الأساسي"
                    )

                except Exception as e:
                    QMessageBox.critical(window, "خطأ", f"فشل في فتح تطبيق الويب:\n{str(e)}")

            # إضافة الدالة للنافذة الرئيسية
            window.open_web_app = open_web_app
            print("✅ تم إضافة زر تطبيق الويب للبرنامج الأساسي")

        except Exception as e:
            print(f"⚠️ لم يتم إضافة زر تطبيق الويب: {e}")

        # تشغيل حلقة الأحداث
        exit_code = app.exec_()

        # إغلاق الأنظمة المتقدمة عند الخروج
        if ADVANCED_SYSTEMS_AVAILABLE:
            try:
                print("🔄 إغلاق الأنظمة المتقدمة...")
                shutdown_advanced_systems()
            except Exception as e:
                print(f"❌ خطأ في إغلاق الأنظمة المتقدمة: {e}")
        else:
            print("ℹ️ لا توجد أنظمة متقدمة لإغلاقها")

        sys.exit(exit_code)

    except Exception as e:
        # معالجة الأخطاء غير المتوقعة
        error_message = f"❌ حدث خطأ غير متوقع: {str(e)}"
        print(error_message)

        # طباعة تفاصيل الخطأ للمطور
        traceback.print_exc()

        # محاولة عرض رسالة الخطأ في نافذة منبثقة
        try:
            QMessageBox.critical(None, "خطأ في البرنامج", error_message)
        except:
            # إذا فشل عرض النافذة المنبثقة، اطبع الخطأ في وحدة التحكم
            print("❌ فشل في عرض نافذة الخطأ")

        sys.exit(1)

if __name__ == "__main__":
    main()
    