"""
نافذة حوار مخصصة لإدخال الباركود يدوياً
"""

from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, 
                            QLineEdit, QPushButton, QTextEdit, QFrame)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont, QIcon


class BarcodeInputDialog(QDialog):
    """نافذة حوار لإدخال الباركود يدوياً"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.barcode_data = None
        self.setup_ui()
        
    def setup_ui(self):
        """إعداد واجهة النافذة"""
        self.setWindowTitle("إدخال باركود يدوياً")
        self.setFixedSize(400, 300)
        self.setModal(True)
        
        # التخطيط الرئيسي
        layout = QVBoxLayout()
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # العنوان
        title_label = QLabel("📷 إدخال باركود يدوياً")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setFont(QFont("Arial", 16, QFont.Bold))
        title_label.setStyleSheet("""
            QLabel {
                color: #2c3e50;
                padding: 10px;
                background-color: #ecf0f1;
                border-radius: 8px;
                margin-bottom: 10px;
            }
        """)
        layout.addWidget(title_label)
        
        # النص التوضيحي
        info_text = QTextEdit()
        info_text.setReadOnly(True)
        info_text.setMaximumHeight(80)
        info_text.setHtml("""
        <div style='text-align: center; font-family: Arial; font-size: 12px;'>
            <p><b>💡 نصائح:</b></p>
            <p>• يمكنك إدخال أي رقم باركود (8 أرقام أو أكثر)</p>
            <p>• أو استخدم الباركود المطبوع على المنتج</p>
        </div>
        """)
        info_text.setStyleSheet("""
            QTextEdit {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 6px;
                padding: 8px;
            }
        """)
        layout.addWidget(info_text)
        
        # حقل إدخال الباركود
        input_layout = QVBoxLayout()
        
        input_label = QLabel("رقم الباركود:")
        input_label.setFont(QFont("Arial", 10, QFont.Bold))
        input_layout.addWidget(input_label)
        
        self.barcode_input = QLineEdit()
        self.barcode_input.setPlaceholderText("أدخل رقم الباركود هنا...")
        self.barcode_input.setFont(QFont("Arial", 12))
        self.barcode_input.setStyleSheet("""
            QLineEdit {
                padding: 10px;
                border: 2px solid #bdc3c7;
                border-radius: 6px;
                font-size: 14px;
            }
            QLineEdit:focus {
                border-color: #3498db;
            }
        """)
        self.barcode_input.textChanged.connect(self.validate_input)
        input_layout.addWidget(self.barcode_input)
        
        layout.addLayout(input_layout)
        
        # خط فاصل
        line = QFrame()
        line.setFrameShape(QFrame.HLine)
        line.setFrameShadow(QFrame.Sunken)
        line.setStyleSheet("color: #bdc3c7;")
        layout.addWidget(line)
        
        # أزرار التحكم
        buttons_layout = QHBoxLayout()
        
        self.cancel_button = QPushButton("❌ إلغاء")
        self.cancel_button.setFont(QFont("Arial", 10))
        self.cancel_button.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 6px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        self.cancel_button.clicked.connect(self.reject)
        
        self.ok_button = QPushButton("✅ موافق")
        self.ok_button.setFont(QFont("Arial", 10))
        self.ok_button.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 6px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #229954;
            }
            QPushButton:disabled {
                background-color: #95a5a6;
            }
        """)
        self.ok_button.setEnabled(False)
        self.ok_button.clicked.connect(self.accept_input)
        
        buttons_layout.addWidget(self.cancel_button)
        buttons_layout.addStretch()
        buttons_layout.addWidget(self.ok_button)
        
        layout.addLayout(buttons_layout)
        
        self.setLayout(layout)
        
        # تركيز على حقل الإدخال
        self.barcode_input.setFocus()
        
    def validate_input(self):
        """التحقق من صحة الإدخال"""
        text = self.barcode_input.text().strip()
        
        # التحقق من أن النص يحتوي على أرقام فقط وطوله مناسب
        is_valid = text.isdigit() and len(text) >= 6
        
        self.ok_button.setEnabled(is_valid)
        
        # تغيير لون الحدود حسب الصحة
        if text:
            if is_valid:
                border_color = "#27ae60"  # أخضر
            else:
                border_color = "#e74c3c"  # أحمر
        else:
            border_color = "#bdc3c7"  # رمادي
            
        self.barcode_input.setStyleSheet(f"""
            QLineEdit {{
                padding: 10px;
                border: 2px solid {border_color};
                border-radius: 6px;
                font-size: 14px;
            }}
            QLineEdit:focus {{
                border-color: {border_color};
            }}
        """)
        
    def accept_input(self):
        """قبول الإدخال"""
        self.barcode_data = self.barcode_input.text().strip()
        self.accept()
        
    def get_barcode(self):
        """الحصول على الباركود المُدخل"""
        return self.barcode_data


def show_barcode_input_dialog(parent=None):
    """عرض نافذة إدخال الباركود"""
    dialog = BarcodeInputDialog(parent)
    
    if dialog.exec_() == QDialog.Accepted:
        return dialog.get_barcode()
    else:
        return None
