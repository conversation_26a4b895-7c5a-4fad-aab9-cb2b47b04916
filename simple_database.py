#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
قاعدة بيانات مبسطة لتطبيق الويب
"""

from sqlalchemy import create_engine, Column, Integer, String, DateTime, Float, Text
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from datetime import datetime
import bcrypt
import os

# إنشاء قاعدة البيانات
DATABASE_URL = "sqlite:///web_business.db"
engine = create_engine(DATABASE_URL, echo=False)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
Base = declarative_base()

# نموذج المستخدم
class User(Base):
    __tablename__ = "users"
    
    id = Column(Integer, primary_key=True, index=True)
    username = Column(String(50), unique=True, index=True)
    password = Column(String(255))
    role = Column(String(20), default="user")
    full_name = Column(String(100))
    email = Column(String(100))
    created_at = Column(DateTime, default=datetime.now)

# نموذج العميل
class Client(Base):
    __tablename__ = "clients"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), nullable=False)
    email = Column(String(100))
    phone = Column(String(20))
    address = Column(Text)
    created_at = Column(DateTime, default=datetime.now)

# نموذج المورد
class Supplier(Base):
    __tablename__ = "suppliers"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), nullable=False)
    email = Column(String(100))
    phone = Column(String(20))
    address = Column(Text)
    created_at = Column(DateTime, default=datetime.now)

# نموذج الفاتورة
class Invoice(Base):
    __tablename__ = "invoices"
    
    id = Column(Integer, primary_key=True, index=True)
    invoice_number = Column(String(50), unique=True)
    client_id = Column(Integer)
    amount = Column(Float, default=0.0)
    status = Column(String(20), default="pending")
    created_at = Column(DateTime, default=datetime.now)

# نموذج المشروع
class Project(Base):
    __tablename__ = "projects"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), nullable=False)
    description = Column(Text)
    status = Column(String(20), default="active")
    budget = Column(Float, default=0.0)
    created_at = Column(DateTime, default=datetime.now)

# نموذج الإيراد
class Revenue(Base):
    __tablename__ = "revenues"
    
    id = Column(Integer, primary_key=True, index=True)
    amount = Column(Float, nullable=False)
    description = Column(Text)
    date = Column(DateTime, default=datetime.now)
    project_id = Column(Integer)

# نموذج المصروف - محسن ومتوافق مع النظام الرئيسي
class Expense(Base):
    __tablename__ = "expenses"

    id = Column(Integer, primary_key=True, index=True)
    title = Column(String(100), nullable=False, index=True)  # متوافق مع النظام الرئيسي
    amount = Column(Float, nullable=False, index=True)
    date = Column(DateTime, default=datetime.now, index=True)
    category = Column(String(100), index=True)
    supplier_id = Column(Integer, nullable=True, index=True)
    client_id = Column(Integer, nullable=True, index=True)
    project_id = Column(Integer, nullable=True, index=True)
    notes = Column(Text)  # متوافق مع النظام الرئيسي

# وظائف المساعدة
def hash_password(password: str) -> str:
    """تشفير كلمة المرور"""
    return bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')

def verify_password(password: str, hashed: str) -> bool:
    """التحقق من كلمة المرور"""
    return bcrypt.checkpw(password.encode('utf-8'), hashed.encode('utf-8'))

def get_session():
    """الحصول على جلسة قاعدة البيانات"""
    return SessionLocal()

def init_db():
    """تهيئة قاعدة البيانات"""
    # إنشاء الجداول
    Base.metadata.create_all(bind=engine)
    
    # إنشاء مستخدم إداري افتراضي
    session = get_session()
    try:
        # التحقق من وجود مستخدم إداري
        admin_user = session.query(User).filter_by(username="admin").first()
        
        if not admin_user:
            admin_user = User(
                username="admin",
                password=hash_password("admin"),
                role="admin",
                full_name="المدير العام",
                email="<EMAIL>"
            )
            session.add(admin_user)
            session.commit()
            print("✅ تم إنشاء المستخدم الإداري")
        
        # إضافة بيانات تجريبية
        add_sample_data(session)
        
    except Exception as e:
        print(f"خطأ في تهيئة قاعدة البيانات: {e}")
        session.rollback()
    finally:
        session.close()

def add_sample_data(session):
    """إضافة بيانات تجريبية"""
    try:
        # إضافة عملاء تجريبيين
        if session.query(Client).count() == 0:
            clients = [
                Client(name="أحمد محمد", email="<EMAIL>", phone="0501234567", address="الرياض"),
                Client(name="فاطمة علي", email="<EMAIL>", phone="0507654321", address="جدة"),
                Client(name="محمد سالم", email="<EMAIL>", phone="0509876543", address="الدمام"),
                Client(name="نورا أحمد", email="<EMAIL>", phone="0502468135", address="مكة"),
                Client(name="خالد عبدالله", email="<EMAIL>", phone="0508642097", address="المدينة")
            ]
            session.add_all(clients)
        
        # إضافة موردين تجريبيين
        if session.query(Supplier).count() == 0:
            suppliers = [
                Supplier(name="شركة التقنية المتقدمة", email="<EMAIL>", phone="0112345678", address="الرياض"),
                Supplier(name="مؤسسة الخدمات التجارية", email="<EMAIL>", phone="0123456789", address="جدة")
            ]
            session.add_all(suppliers)
        
        # إضافة مشاريع تجريبية
        if session.query(Project).count() == 0:
            projects = [
                Project(name="تطوير موقع إلكتروني", description="تطوير موقع تجاري", budget=50000.0),
                Project(name="نظام إدارة المخزون", description="تطوير نظام إدارة المخزون", budget=75000.0)
            ]
            session.add_all(projects)
        
        # إضافة فواتير تجريبية
        if session.query(Invoice).count() == 0:
            invoices = [
                Invoice(invoice_number="INV-001", client_id=1, amount=15000.0, status="paid"),
                Invoice(invoice_number="INV-002", client_id=2, amount=25000.0, status="pending"),
                Invoice(invoice_number="INV-003", client_id=3, amount=8500.0, status="paid")
            ]
            session.add_all(invoices)
        
        # إضافة إيرادات ومصروفات تجريبية
        if session.query(Revenue).count() == 0:
            revenues = [
                Revenue(amount=15000.0, description="دفعة من العميل أحمد محمد", project_id=1),
                Revenue(amount=25000.0, description="دفعة من العميل فاطمة علي", project_id=2),
                Revenue(amount=8500.0, description="دفعة من العميل محمد سالم", project_id=1)
            ]
            session.add_all(revenues)
        
        if session.query(Expense).count() == 0:
            expenses = [
                Expense(
                    title="شراء معدات مكتبية",
                    amount=5000.0,
                    category="أدوات",
                    notes="شراء معدات مكتبية للمشروع",
                    project_id=1
                ),
                Expense(
                    title="رسوم استضافة",
                    amount=3500.0,
                    category="مرافق",
                    notes="رسوم استضافة الموقع",
                    project_id=1
                ),
                Expense(
                    title="رواتب المطورين",
                    amount=7500.0,
                    category="رواتب",
                    notes="رواتب فريق التطوير",
                    project_id=2
                )
            ]
            session.add_all(expenses)
        
        session.commit()
        print("✅ تم إضافة البيانات التجريبية")
        
    except Exception as e:
        print(f"خطأ في إضافة البيانات التجريبية: {e}")
        session.rollback()

if __name__ == "__main__":
    print("🔧 تهيئة قاعدة البيانات...")
    init_db()
    print("✅ تم إعداد قاعدة البيانات بنجاح")
